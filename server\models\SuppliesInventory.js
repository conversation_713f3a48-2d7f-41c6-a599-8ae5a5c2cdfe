/* 备件库存 */
const mongoose = require("mongoose");

const suppliesInventorySchema = new mongoose.Schema(
  {
    id: {
      type: String,
      required: true,
      unique: true,
    },
    // 备件ID
    suppliesId: {
      type: String,
      required: true,
    },
    // 库存数量
    stock: {
      type: Number,
      default: 0,
    },
    // 预扣库存
    preDeductedStock: {
      type: Number,
      default: 0,
    },
    // 库房ID
    warehouseId: {
      type: String,
      required: true,
    },
    // 安全库存
    safeStock: {
      type: Number,
      default: 0,
    },
    // 预警状态
    warningStatus: {
      type: String,
      enum: ["NORMAL", "WARNING"],
      default: "NORMAL",
    },
  },
  {
    timestamps: true,
  }
);
const SuppliesInventory = mongoose.model(
  "SuppliesInventory",
  suppliesInventorySchema
);

module.exports = { SuppliesInventory, suppliesInventorySchema };
