import { createRouter, createWebHistory } from "vue-router";
import { useUserStore } from "@/store";
const routes = [
  {
    path: "/",
    name: "Login",
    component: () => import("../views/Login.vue"),
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("../views/Register.vue"),
  },
  {
    path: "/",
    component: () => import("../views/Layout.vue"),
    meta: { requiresAuth: true },
    children: [
      {
        path: "home",
        name: "Home",
        component: () => import("../views/Home.vue"),
        meta: { title: "首页" },
      },
      {
        path: "userinfo",
        name: "UserInfo",
        component: () => import("../views/userinfo/index.vue"),
        meta: { title: "个人中心" },
      },
      {
        path: "workorder",
        name: "WorkOrder",
        component: () => import("../views/workorder/Index.vue"),
        meta: { title: "工单管理" },
      },
      {
        path: "workorder/detail",
        name: "WorkOrderDetail",
        component: () => import("../views/workorder/OrderDetail.vue"),
        meta: { title: "工单详情" },
      },
      {
        // 维保单位管理
        path: "maintenance-enterprise",
        name: "MaintenanceEnterprise",
        redirect: "/maintenance-enterprise/admission",
        meta: { title: "维保单位管理" },
        children: [
          {
            path: "admission",
            name: "Admission",
            component: () =>
              import("../views/maintenance-enterprise/Admission.vue"),
            meta: { title: "入驻管理" },
          },
          {
            path: "staff",
            name: "OrganizationStaff",
            component: () =>
              import("../views/maintenance-enterprise/Staff.vue"),
            meta: { title: "维保人员管理" },
          },
          {
            path: "staff-detail",
            name: "StaffDetail",
            component: () =>
              import("../views/maintenance-enterprise/StaffDetail.vue"),
            meta: { title: "维保人员详情" },
          },
          {
            path: "admission-detail",
            name: "AdmissionDetail",
            component: () =>
              import("../views/maintenance-enterprise/AdmissionDetail.vue"),
            meta: { title: "入驻单位详情" },
          },
          {
            path: "settlement-config",
            name: "MaintenanceSettlementConfig",
            component: () =>
              import("../views/maintenance-enterprise/SettlementConfig.vue"),
            meta: { title: "结算配置" },
          },
        ],
      },
      {
        // 客户单位管理
        path: "customer",
        name: "Customer",
        redirect: "/customer/unit",
        meta: { title: "客户单位管理" },
        children: [
          {
            path: "unit",
            name: "CustomerUnit",
            component: () => import("../views/customer/Unit.vue"),
            meta: { title: "客户单位管理" },
          },
          {
            path: "unit-detail",
            name: "CustomerUnitDetail",
            component: () => import("../views/customer/UnitDetail.vue"),
            meta: { title: "客户单位详情" },
          },
          {
            path: "staff",
            name: "CustomerStaff",
            component: () => import("../views/customer/Staff.vue"),
            meta: { title: "报修人员管理" },
          },
          {
            path: "settlement-config",
            name: "SettlementConfig",
            component: () => import("../views/customer/SettlementConfig.vue"),
            meta: { title: "结算配置" },
          },
        ],
      },
      {
        // 备件管理
        path: "inventory",
        name: "Inventory",
        component: () => import("../views/inventory/Index.vue"),
        redirect: "/inventory/category",
        meta: { title: "备件管理" },
        children: [
          {
            path: "category",
            name: "Category",
            component: () => import("../views/inventory/Category.vue"),
            meta: { title: "类别设置" },
          },
          {
            path: "list",
            name: "MaterialList",
            component: () => import("../views/inventory/SuppliesList.vue"),
            meta: { title: "备件清单" },
          },
          {
            path: "storage",
            name: "Storage",
            component: () => import("../views/inventory/Storage.vue"),
            meta: { title: "备件库存" },
          },
          {
            path: "warehouse",
            name: "Warehouse",
            meta: { title: "入库/出库/调拨" },
            children: [
              {
                path: "inbound",
                name: "Inbound",
                component: () => import("../views/inventory/Inbound.vue"),
                meta: { title: "入库管理" },
              },
              {
                path: "outbound",
                name: "Outbound",
                component: () => import("../views/inventory/Outbound.vue"),
                meta: { title: "出库管理" },
              },
              {
                path: "transfer",
                name: "Transfer",
                component: () => import("../views/inventory/Transfer.vue"),
                meta: { title: "调拨管理" },
              },
            ],
          },
        ],
      },
      {
        path: "statistics",
        name: "Statistics",
        component: () => import("../views/statistics/Index.vue"),
        meta: { title: "统计管理" },
      },
      {
        path: "contract",
        name: "Contract",
        component: () => import("../views/contract/Index.vue"),
        meta: { title: "合同管理" },
      },
      {
        // 系统设置
        path: "settings",
        name: "Settings",
        redirect: "/settings/dictionary",
        meta: { title: "系统设置" },
        children: [
          {
            path: "dictionary",
            name: "Dictionary",
            component: () => import("../views/settings/Dictionary.vue"),
            meta: { title: "字典配置" },
          },
          {
            path: "service-class",
            name: "ServiceClass",
            component: () => import("../views/settings/ServiceClass.vue"),
            meta: { title: "服务内容项目配置" },
          },
        ],
      },
      {
        // 用户管理
        path: "user",
        name: "User",
        component: () => import("../views/user/Index.vue"),
        redirect: "/user/list",
        meta: { title: "用户管理" },
        children: [
          {
            path: "list",
            name: "UserList",
            component: () => import("../views/user/List.vue"),
            meta: { title: "用户管理" },
          },
          {
            path: "role",
            name: "Role",
            component: () => import("../views/user/Role.vue"),
            meta: { title: "角色管理" },
          },
          {
            path: "permission",
            name: "Permission",
            component: () => import("../views/user/Permission.vue"),
            meta: { title: "权限管理" },
          },
        ],
      },
      {
        path: "settlement",
        name: "Settlement",
        component: () => import("../views/settlement/Index.vue"),
        meta: { title: "结算管理" },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = useUserStore().token;

  if (to.matched.some((record) => record.meta.requiresAuth)) {
    // 需要登录权限的路由
    if (!token) {
      next({ path: "/" });
    } else {
      next();
    }
  } else {
    // 不需要登录权限的路由
    if (token && (to.path === "/" || to.path === "/register")) {
      // 已登录用户访问登录或注册页面，重定向到首页
      next({ path: "/home" });
    } else {
      next();
    }
  }
});

export default router;
