<!-- 新增/编辑类别弹窗 -->
<template>
  <CommonDialog
    v-model:visible="visible"
    :title="type === 'add' ? '新增类别' : '编辑类别'"
    :btn-loading="loading"
    :confirm-callback="handleConfirm"
  >
    <el-form label-width="80px" :model="form" :rules="rules">
      <el-form-item label="类别名称" prop="name">
        <el-input v-model.trim="form.name" />
      </el-form-item>
      <el-form-item label="类别编码" prop="code">
        <el-input v-model.trim="form.code" />
      </el-form-item>
      <el-form-item label="类别描述" prop="remark">
        <el-input
          v-model.trim="form.remark"
          type="textarea"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </CommonDialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { CommonDialog } from "@/base-components";
import { createSuppliesClass<PERSON>pi, editSupplies<PERSON>lass<PERSON><PERSON> } from "@/api";
import { toastError, Message } from "@/utils";

const emits = defineEmits(["refresh"]);

const type = ref<"add" | "edit">("add");
const visible = ref(false);

const form = reactive({
  name: "",
  code: "",
  remark: "",
  id: "",
  parentId: "",
});

const rules = {
  name: [{ required: true, message: "请输入类别名称", trigger: "blur" }],
  code: [{ required: true, message: "请输入类别编码", trigger: "blur" }],
};
const loading = ref(false);
function handleConfirm() {
  loading.value = true;
  let requestApi =
    type.value === "add" ? createSuppliesClassApi : editSuppliesClassApi;
  requestApi(form)
    .then((res) => {
      visible.value = false;
      loading.value = false;
      Message.success(type.value === "add" ? "新建类别成功" : "编辑类别成功");
      emits("refresh");
    })
    .catch((err) => {
      toastError(err, type.value === "add" ? "新建类别失败" : "编辑类别失败");
      loading.value = false;
    });
}

defineExpose({
  open: (dialogType: "add" | "edit", row: any = null) => {
    visible.value = true;
    type.value = dialogType;
    if (dialogType === "edit") {
      form.name = row.name || "";
      form.code = row.code || "";
      form.remark = row.remark || "";
      form.id = row.id || "";
      form.parentId = row.parentId || "";
    } else if (dialogType === "add") {
      form.name = "";
      form.code = "";
      form.remark = "";
      form.parentId = row.id || "";
    }
  },
});
</script>
