<template>
  <div class="supplies-list-root">
    <div class="supplies-list-header">
      <div class="supplies-list-header-title">
        <span>备件管理</span>
      </div>
    </div>
    <div class="supplies-list-container">
      <div class="supplies-list-content">
        <div class="supplies-list-sidebar">
          <div class="sidebar-content">
            <el-tree
              :data="suppliesClassList"
              :props="{
                children: 'children',
                label: 'name',
              }"
              node-key="id"
              :current-node-key="currentSuppliesClass?.id"
              :highlight-current="true"
              :expand-on-click-node="false"
              @node-click="handleSelectSuppliesClass"
            >
            </el-tree>
          </div>
        </div>
        <div class="supplies-list-table-container">
          <div class="supplies-list-search">
            <SearchContainer
              @queryBtnClick="handleSearch"
              @resetBtnClick="handleReset"
            >
              <el-form-item label="备件编号/名称">
                <el-input
                  v-model.trim="searchForm.keyword"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </SearchContainer>
          </div>

          <CommonTable
            ref="tableRef"
            class="supplies-list-table"
            :columns="suppliesColumns"
            :requestApi="getSuppliesListApi"
            :requestParams="requestParams"
            :dataCallback="dataCallback"
          >
            <template #header>
              <el-button type="primary" :icon="Plus" @click="handleAddSupplies">
                新建备件
              </el-button>
              <el-button @click="handleImport">导入</el-button>
              <el-button @click="handleExport">导出</el-button>
            </template>
            <template #operations="{ row }">
              <el-button link type="primary" @click="handleEdit(row)">
                查看
              </el-button>
              <el-button link type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </CommonTable>
        </div>
      </div>
    </div>

    <!-- 新增/编辑备件弹窗 -->
    <CommonDialog
      v-model:visible="suppliesDialog.visible"
      :title="suppliesDialog.type === 'edit' ? '编辑备件' : '新增备件'"
      :confirm-callback="submitSupplies"
      :btn-loading="submitLoading"
      width="600px"
    >
      <el-form
        ref="suppliesFormRef"
        :model="suppliesForm"
        :rules="suppliesRules"
        label-width="100px"
      >
        <el-form-item label="备件类型" prop="suppliesType">
          <el-select
            v-model="suppliesForm.suppliesType"
            placeholder="请选择备件类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in flatSuppliesClassList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备件编号" prop="suppliesCode">
          <el-input
            v-model.trim="suppliesForm.suppliesCode"
            placeholder="请输入备件编号"
          />
        </el-form-item>
        <el-form-item label="备件名称" prop="suppliesName">
          <el-input
            v-model.trim="suppliesForm.suppliesName"
            placeholder="请输入备件名称"
          />
        </el-form-item>
        <el-form-item label="规格" prop="specification">
          <el-input
            v-model.trim="suppliesForm.specification"
            placeholder="请输入规格"
          />
        </el-form-item>
        <el-form-item label="型号" prop="model">
          <el-input
            v-model.trim="suppliesForm.model"
            placeholder="请输入型号"
          />
        </el-form-item>
        <el-form-item label="成本价格" prop="costPrice">
          <el-input-number
            v-model="suppliesForm.costPrice"
            :precision="2"
            :min="0"
            :step="0.01"
            controls-position="right"
            placeholder="请输入成本价格"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="计量单位" prop="unit">
          <el-input
            v-model.trim="suppliesForm.unit"
            placeholder="请输入计量单位"
          />
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { SearchContainer, CommonDialog, CommonTable } from "@/base-components";
import { Message, SystemPrompt, toastError } from "@/utils";
import { cloneDeep } from "lodash";
import { getSuppliesClassTreeApi } from "@/api";
import { suppliesColumns } from "./config";

/* ====================================== 备件分类 ====================================== */
const suppliesClassList = ref([{ name: "全部", id: "" }]);
const currentSuppliesClass = ref({ name: "全部", id: "" });

onMounted(() => {
  refreshSuppliesClassList();
});

const classLoading = ref(false);
function refreshSuppliesClassList() {
  classLoading.value = true;
  getSuppliesClassTreeApi()
    .then((res) => {
      const data = res.data.data || [];
      suppliesClassList.value = [{ name: "全部", id: "" }].concat(data);
      classLoading.value = false;
    })
    .catch((err) => {
      classLoading.value = false;
      toastError(err, "获取备件分类失败");
    });
}

// 扁平化分类列表，用于表单选择
const flatSuppliesClassList = computed(() => {
  const flatten = (list, result = []) => {
    list.forEach((item) => {
      result.push(item);
      if (item.children && item.children.length > 0) {
        flatten(item.children, result);
      }
    });
    return result;
  };
  return flatten(suppliesClassList.value);
});

// 选择备件分类
const handleSelectSuppliesClass = (suppliesClass) => {
  currentSuppliesClass.value = suppliesClass;
  handleReset();
};

/* ====================================== 备件列表 ====================================== */
// 搜索表单
const searchForm = reactive({
  keyword: "",
});

const tableRef = ref();
const requestParams = reactive({ filters: "" });

function dataCallback(res) {
  const data = res.data?.data?.rows || [];
  return {
    tableRows: data,
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

// 搜索
const handleSearch = () => {
  requestParams.filters = `keyword=${searchForm.keyword},category=${
    currentSuppliesClass.value?.name || ""
  }`;
};

// 重置
const handleReset = () => {
  searchForm.keyword = "";
  requestParams.filters = `keyword=,category=${
    currentSuppliesClass.value?.name || ""
  }`;
};

/* ====================================== 备件操作 ====================================== */
// 备件表单
const suppliesDialog = reactive({
  visible: false,
  type: "add",
});

const initSuppliesForm = {
  id: "",
  suppliesType: "",
  suppliesCode: "",
  suppliesName: "",
  specification: "",
  model: "",
  costPrice: null,
  unit: "",
};

const suppliesForm = ref(cloneDeep(initSuppliesForm));

const suppliesRules = {
  suppliesType: [
    { required: true, message: "请选择备件类型", trigger: "change" },
  ],
  suppliesCode: [
    { required: true, message: "请输入备件编号", trigger: "blur" },
  ],
  suppliesName: [
    { required: true, message: "请输入备件名称", trigger: "blur" },
  ],
  specification: [{ required: true, message: "请输入规格", trigger: "blur" }],
  unit: [{ required: true, message: "请输入计量单位", trigger: "blur" }],
};

const suppliesFormRef = ref();
const submitLoading = ref(false);

// 新增备件
const handleAddSupplies = () => {
  suppliesForm.value = cloneDeep(initSuppliesForm);
  suppliesDialog.type = "add";
  suppliesDialog.visible = true;
  suppliesFormRef.value?.clearValidate();
};

// 编辑备件
const handleEdit = (row) => {
  suppliesForm.value = cloneDeep(row);
  suppliesDialog.type = "edit";
  suppliesDialog.visible = true;
  suppliesFormRef.value?.clearValidate();
};

// 删除备件
const handleDelete = (row) => {
  SystemPrompt(
    "删除备件后，将连同库存一起删除，确定要删除吗？",
    "warning"
  ).then(() => {
    // 模拟删除操作
    Message.success("删除成功");
    tableRef.value.refreshTableData();
  });
};

// 提交备件
const submitSupplies = () => {
  suppliesFormRef.value.validate((valid) => {
    if (!valid) return;

    submitLoading.value = true;

    // 模拟API调用
    setTimeout(() => {
      Message.success(suppliesDialog.type === "edit" ? "编辑成功" : "新增成功");
      suppliesDialog.visible = false;
      submitLoading.value = false;
      tableRef.value.refreshTableData();
    }, 1000);
  });
};

// 导入
const handleImport = () => {
  Message.info("导入功能待开发");
};

// 导出
const handleExport = () => {
  Message.info("导出功能待开发");
};
</script>

<style lang="less" scoped>
.supplies-list-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .supplies-list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;

    .supplies-list-header-title {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .supplies-list-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .supplies-list-content {
      flex: 1;
      min-height: 0px;
      display: flex;
      gap: 20px;

      .supplies-list-sidebar {
        width: 300px;
        background-color: #f5f5f5;
        border-radius: 10px;
        display: flex;
        flex-direction: column;

        .sidebar-content {
          flex: 1;
          overflow-y: auto;
          padding: 10px;
          min-height: 0;

          :deep(.el-tree) {
            background: transparent;

            .el-tree-node {
              .el-tree-node__content {
                padding: 8px 0;
                border-radius: 6px;
                margin-bottom: 2px;

                &:hover {
                  background-color: #e8e8e8;
                }
              }

              &.is-current > .el-tree-node__content {
                background-color: #e6f7ff;
                color: #1890ff;
                font-weight: bold;
                position: relative;

                &:before {
                  content: "";
                  position: absolute;
                  left: 0;
                  top: 0;
                  bottom: 0;
                  width: 3px;
                  background-color: #1890ff;
                  border-radius: 0 3px 3px 0;
                }
              }
            }

            .tree-node-label {
              font-size: 14px;
            }
          }
        }
      }

      .supplies-list-table-container {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;

        .supplies-list-search {
          flex-shrink: 0;
          background: #f5f5f5;
          border-radius: 10px;
          padding: 0px 20px;
          box-sizing: border-box;
          margin-bottom: 10px;
        }

        .supplies-list-table {
          flex: 1;
          min-height: 0;
        }
      }
    }
  }
}
</style>
