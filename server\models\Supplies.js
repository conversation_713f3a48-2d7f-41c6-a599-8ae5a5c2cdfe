const mongoose = require("mongoose");

const suppliesSchema = new mongoose.Schema(
  {
    id: {
      type: String,
      required: true,
      unique: true,
    },
    // 备件类型
    classId: {
      type: String,
      required: true,
    },
    // 备件编号
    suppliesCode: {
      type: String,
      required: true,
      unique: true,
    },
    // 备件名称
    suppliesName: {
      type: String,
      required: true,
      unique: true,
    },
    // 规格
    specification: {
      type: String,
    },
    // 型号
    model: {
      type: String,
    },
    // 计量单位
    unit: {
      type: String,
    },
    // 成本价
    costPrice: {
      type: [Number],
      required: true,
    },
  },
  {
    timestamps: true,
  }
);
const Supplies = mongoose.model("Supplies", suppliesSchema);

module.exports = Supplies;
