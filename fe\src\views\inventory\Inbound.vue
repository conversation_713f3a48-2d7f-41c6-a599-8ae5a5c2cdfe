<template>
  <div class="inbound-root">
    <!-- <div class="inbound-tabs">
      <TabsRouter :tabsConfig="LogRoutes" />
    </div> -->
    <div class="inbound-header">
      <div class="inbound-header-title">
        <TabsRouter :tabsConfig="LogRoutes" />
        <!-- <span>入库管理</span> -->
      </div>

      <div class="inbound-header-action">
        <el-button type="primary" :icon="Plus" @click="handleAddInbound">
          新建入库
        </el-button>
      </div>
    </div>
    <div class="inbound-container">
      <div class="inbound-search">
        <SearchContainer
          @queryBtnClick="handleSearch"
          @resetBtnClick="handleReset"
        >
          <el-form-item label="入库单编号">
            <el-input
              v-model.trim="searchForm.inboundNumber"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="入库时间">
            <el-date-picker
              v-model="searchForm.inboundDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="入库类型">
            <el-select
              v-model="searchForm.inboundType"
              placeholder="全部"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="采购入库" value="PURCHASE" />
              <el-option label="退货入库" value="RETURN" />
              <el-option label="调拨入库" value="TRANSFER" />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select
              v-model="searchForm.auditStatus"
              placeholder="全部"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="待审核" value="PENDING" />
              <el-option label="已通过" value="APPROVED" />
              <el-option label="已拒绝" value="REJECTED" />
            </el-select>
          </el-form-item>
        </SearchContainer>
      </div>
      <div class="inbound-content">
        <CommonTable
          ref="tableRef"
          :columns="inboundColumns"
          :requestApi="getInboundListApi"
          :requestParams="requestParams"
          :dataCallback="dataCallback"
        >
          <template #inboundType="{ row }">
            <el-tag :type="getInboundTypeTag(row.inboundType)">
              {{ getInboundTypeText(row.inboundType) }}
            </el-tag>
          </template>
          <template #auditStatus="{ row }">
            <el-tag :type="getAuditStatusTag(row.auditStatus)">
              {{ getAuditStatusText(row.auditStatus) }}
            </el-tag>
          </template>
          <template #operations="{ row }">
            <el-button link type="primary" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.auditStatus === 'PENDING'"
              link
              type="primary"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.auditStatus === 'PENDING'"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </CommonTable>
      </div>
    </div>

    <!-- 新增/编辑入库单弹窗 -->
    <CommonDialog
      v-model:visible="inboundDialog.visible"
      :title="inboundDialog.type === 'edit' ? '编辑入库单' : '新建入库单'"
      :confirm-callback="submitInbound"
      :btn-loading="submitLoading"
      width="800px"
    >
      <el-form
        ref="inboundFormRef"
        :model="inboundForm"
        :rules="inboundRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入库单编号" prop="inboundNumber">
              <el-input
                v-model.trim="inboundForm.inboundNumber"
                placeholder="系统自动生成"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库类型" prop="inboundType">
              <el-select
                v-model="inboundForm.inboundType"
                placeholder="请选择入库类型"
                style="width: 100%"
              >
                <el-option label="采购入库" value="PURCHASE" />
                <el-option label="退货入库" value="RETURN" />
                <el-option label="调拨入库" value="TRANSFER" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入库仓库" prop="warehouse">
              <el-select
                v-model="inboundForm.warehouse"
                placeholder="请选择仓库"
                style="width: 100%"
              >
                <el-option label="成都仓库" value="CHENGDU" />
                <el-option label="重庆仓库" value="CHONGQING" />
                <el-option label="西安仓库" value="XIAN" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="制单人" prop="creator">
              <el-input
                v-model.trim="inboundForm.creator"
                placeholder="请输入制单人"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model.trim="inboundForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Plus } from "@element-plus/icons-vue";
import {
  TabsRouter,
  SearchContainer,
  CommonTable,
  CommonDialog,
} from "@/base-components";
import { LogRoutes } from "./config";
import { Message, SystemPrompt } from "@/utils";
import { cloneDeep } from "lodash";

/* ====================================== 表格配置 ====================================== */
const inboundColumns = [
  {
    prop: "inboundNumber",
    label: "入库单编号",
    width: 180,
    checked: true,
  },
  {
    prop: "inboundDate",
    label: "入库日期",
    width: 120,
    checked: true,
  },
  {
    prop: "inboundWarehouse",
    label: "入库仓库",
    width: 120,
    checked: true,
  },
  {
    prop: "inboundType",
    label: "入库类型",
    width: 120,
    checked: true,
  },
  {
    prop: "suppliesQuantity",
    label: "交货数量 (人数)",
    width: 150,
    checked: true,
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120,
    checked: true,
  },
  {
    prop: "creator",
    label: "制单人",
    width: 100,
    checked: true,
  },
  {
    prop: "createTime",
    label: "制单时间",
    width: 160,
    checked: true,
  },
  {
    prop: "operations",
    label: "操作",
    width: 180,
    fixed: "right",
    checked: true,
  },
];

/* ====================================== 搜索和数据 ====================================== */
const searchForm = reactive({
  inboundNumber: "",
  inboundDate: [],
  inboundType: "",
  auditStatus: "",
});

const requestParams = reactive({
  filters: "",
});

const tableRef = ref();

// 模拟API函数
const getInboundListApi = (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟数据
      const mockData = [
        {
          id: 1,
          inboundNumber: "RK20250228001",
          inboundDate: "2025-02-28",
          inboundWarehouse: "成都仓库",
          inboundType: "PURCHASE",
          suppliesQuantity: "张月丹",
          auditStatus: "PENDING",
          creator: "张月丹",
          createTime: "2025-02-28 15:43:26",
        },
        {
          id: 2,
          inboundNumber: "RK20250227001",
          inboundDate: "2025-02-27",
          inboundWarehouse: "重庆仓库",
          inboundType: "RETURN",
          suppliesQuantity: "张月丹",
          auditStatus: "APPROVED",
          creator: "张月丹",
          createTime: "2025-02-27 15:43:26",
        },
        {
          id: 3,
          inboundNumber: "RK20250226001",
          inboundDate: "2025-02-26",
          inboundWarehouse: "西安仓库",
          inboundType: "TRANSFER",
          suppliesQuantity: "阿文",
          auditStatus: "APPROVED",
          creator: "阿文",
          createTime: "2025-02-26 15:43:26",
        },
        {
          id: 4,
          inboundNumber: "RK20250225001",
          inboundDate: "2025-02-25",
          inboundWarehouse: "西安仓库",
          inboundType: "TRANSFER",
          suppliesQuantity: "阿文",
          auditStatus: "APPROVED",
          creator: "阿文",
          createTime: "2025-02-25 15:43:26",
        },
      ];

      let filteredData = [...mockData];

      // 根据搜索条件过滤
      if (searchForm.inboundNumber) {
        filteredData = filteredData.filter((item) =>
          item.inboundNumber.includes(searchForm.inboundNumber)
        );
      }

      if (searchForm.inboundType) {
        filteredData = filteredData.filter(
          (item) => item.inboundType === searchForm.inboundType
        );
      }

      if (searchForm.auditStatus) {
        filteredData = filteredData.filter(
          (item) => item.auditStatus === searchForm.auditStatus
        );
      }

      const total = filteredData.length;
      const { offset = 0, limit = 10 } = params;
      const start = offset;
      const end = start + limit;
      const rows = filteredData.slice(start, end);

      resolve({
        data: {
          data: {
            rows,
            pageElements: {
              totalElements: total,
            },
          },
        },
      });
    }, 500);
  });
};

// CommonTable数据回调函数
function dataCallback(res) {
  const data = res.data?.data?.rows || [];
  return {
    tableRows: data,
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

/* ====================================== 搜索操作 ====================================== */
// 搜索
const handleSearch = () => {
  const filters = [];
  for (let key in searchForm) {
    if (searchForm[key]) {
      if (
        key === "inboundDate" &&
        Array.isArray(searchForm[key]) &&
        searchForm[key].length === 2
      ) {
        filters.push(`startDate=${searchForm[key][0]}`);
        filters.push(`endDate=${searchForm[key][1]}`);
      } else {
        filters.push(`${key}=${searchForm[key]}`);
      }
    }
  }
  requestParams.filters = filters.join(",");
};

// 重置
const handleReset = () => {
  searchForm.inboundNumber = "";
  searchForm.inboundDate = [];
  searchForm.inboundType = "";
  searchForm.auditStatus = "";
  requestParams.filters = "";
};

/* ====================================== 状态处理函数 ====================================== */
// 入库类型标签
const getInboundTypeTag = (type) => {
  const tagMap = {
    PURCHASE: "primary",
    RETURN: "warning",
    TRANSFER: "success",
  };
  return tagMap[type] || "";
};

const getInboundTypeText = (type) => {
  const textMap = {
    PURCHASE: "采购入库",
    RETURN: "退货入库",
    TRANSFER: "调拨入库",
  };
  return textMap[type] || type;
};

// 审核状态标签
const getAuditStatusTag = (status) => {
  const tagMap = {
    PENDING: "warning",
    APPROVED: "success",
    REJECTED: "danger",
  };
  return tagMap[status] || "";
};

const getAuditStatusText = (status) => {
  const textMap = {
    PENDING: "待审核",
    APPROVED: "已通过",
    REJECTED: "已拒绝",
  };
  return textMap[status] || status;
};

/* ====================================== 入库单操作 ====================================== */
// 入库单表单
const inboundDialog = reactive({
  visible: false,
  type: "add",
});

const initInboundForm = {
  id: "",
  inboundNumber: "",
  inboundType: "",
  warehouse: "",
  creator: "",
  remark: "",
};

const inboundForm = ref(cloneDeep(initInboundForm));

const inboundRules = {
  inboundType: [
    { required: true, message: "请选择入库类型", trigger: "change" },
  ],
  warehouse: [{ required: true, message: "请选择入库仓库", trigger: "change" }],
  creator: [{ required: true, message: "请输入制单人", trigger: "blur" }],
};

const inboundFormRef = ref();
const submitLoading = ref(false);

// 新增入库单
const handleAddInbound = () => {
  inboundForm.value = cloneDeep(initInboundForm);
  inboundForm.value.inboundNumber = generateInboundNumber();
  inboundDialog.type = "add";
  inboundDialog.visible = true;
  inboundFormRef.value?.clearValidate();
};

// 编辑入库单
const handleEdit = (row) => {
  inboundForm.value = cloneDeep(row);
  inboundDialog.type = "edit";
  inboundDialog.visible = true;
  inboundFormRef.value?.clearValidate();
};

// 查看入库单
const handleView = (row) => {
  Message.info("查看功能待开发");
};

// 删除入库单
const handleDelete = (row) => {
  SystemPrompt("是否删除该入库单？", "warning").then(() => {
    Message.success("删除成功");
    tableRef.value.refreshTableData();
  });
};

// 提交入库单
const submitInbound = () => {
  inboundFormRef.value.validate((valid) => {
    if (!valid) return;

    submitLoading.value = true;

    // 模拟API调用
    setTimeout(() => {
      Message.success(inboundDialog.type === "edit" ? "编辑成功" : "新增成功");
      inboundDialog.visible = false;
      submitLoading.value = false;
      tableRef.value.refreshTableData();
    }, 1000);
  });
};

// 生成入库单编号
const generateInboundNumber = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const random = String(Math.floor(Math.random() * 1000)).padStart(3, "0");
  return `RK${year}${month}${day}${random}`;
};
</script>

<style lang="less" scoped>
.inbound-root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .inbound-tabs {
    flex-shrink: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .inbound-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 0px 20px 0 0;
    flex-shrink: 0;

    .inbound-header-title {
      :deep(.custom-tabs-header) {
        height: 50px;
        & a {
          padding: 16px;
        }
      }
    }

    .inbound-header-action {
      display: flex;
      gap: 10px;
    }
  }

  .inbound-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .inbound-search {
      flex-shrink: 0;
      background: #f5f5f5;
      border-radius: 10px;
      padding: 0px 20px;
      box-sizing: border-box;
      margin-bottom: 10px;
    }

    .inbound-content {
      flex: 1;
      min-height: 0px;
    }
  }
}

/* 弹窗样式优化 */
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #333;
        font-weight: 600;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      tr {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 按钮样式优化 */
:deep(.el-button) {
  &.is-link {
    padding: 0;
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}

/* 搜索容器样式优化 */
:deep(.search-container) {
  .el-form {
    .el-form-item {
      margin-bottom: 16px;

      .el-form-item__label {
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
