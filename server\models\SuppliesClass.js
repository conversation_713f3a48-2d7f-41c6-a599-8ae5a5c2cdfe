/* 备件类别 */
const mongoose = require("mongoose");

const suppliesClassSchema = new mongoose.Schema(
  {
    // 类别ID
    id: {
      type: String,
      required: true,
      unique: true,
    },
    // 父节点ID
    parentId: {
      type: String,
      default: "", // 顶级类别的parentId为""
    },
    // 类别编码
    code: {
      type: String,
      required: true,
      unique: true,
    },
    // 类别名称
    name: {
      type: String,
      required: true,
    },
    // 备注
    remark: {
      type: String,
    },
    // 排序顺序
    sort: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);
const SuppliesClass = mongoose.model("SuppliesClass", suppliesClassSchema);

module.exports = {
  SuppliesClass,
  suppliesClassSchema,
};
