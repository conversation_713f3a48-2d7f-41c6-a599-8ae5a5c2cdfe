const express = require("express");
const router = express.Router();
const Supplies = require("../controllers/supplies");
const { protect } = require("../middleware/auth");

// 获取备件类别
router.get("/get-supplies-class", protect, Supplies.getSuppliesClass);

// 添加备件类别
router.post("/add-supplies-class", protect, Supplies.addSuppliesClass);

// 编辑备件类别
router.put("/edit-supplies-class", protect, Supplies.editSuppliesClass);

// 删除备件类别
router.delete(
  "/delete-supplies-class/:id",
  protect,
  Supplies.deleteSuppliesClass
);

// 上移/下移备件类别
router.put("/move-supplies-class", protect, Supplies.moveSuppliesClass);

module.exports = router;
