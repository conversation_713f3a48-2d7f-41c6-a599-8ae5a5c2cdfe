/* 库存日志 */
const mongoose = require("mongoose");

const suppliesInventoryLogSchema = new mongoose.Schema(
  {
    id: {
      type: String,
      required: true,
      unique: true,
    },
    // 操作人
    operatorUserId: {
      type: String,
      required: true,
    },
    // 操作类型
    operationType: {
      type: String,
      //   入库、出库、调拨
      enum: ["INBOUND", "OUTBOUND", "TRANSFER"],
      required: true,
    },
    // 操作日期
    operationDate: {
      type: Number,
      required: true,
    },
    // 入库库房
    inboundWarehouseId: {
      type: String,
    },
    // 出库库房
    outboundWarehouseId: {
      type: String,
    },
    // 供科单位
    supplierId: {
      type: String,
    },
    // 收科单位
    receiverId: {
      type: String,
    },
    // 备注
    remark: {
      type: String,
    },
    // 备件明细
    suppliesInfo: [
      {
        // 备件ID
        suppliesId: { type: String, required: true, sparse: true },
        // 入库数量
        inboundQuantity: { type: Number, default: 0 },
        // 入库单价
        inboundUnitPrice: { type: Number, default: 0 },
        // 入库金额
        inboundAmount: { type: Number, default: 0 },
        // 出库数量
        outboundQuantity: { type: Number, default: 0 },
        // 出库单价
        outboundUnitPrice: { type: Number, default: 0 },
        // 出库金额
        outboundAmount: { type: Number, default: 0 },
        // 调出数量
        transferOutQuantity: { type: Number, default: 0 },
        // 调拨单价
        transferUnitPrice: { type: Number, default: 0 },
        // 调拨金额
        transferAmount: { type: Number, default: 0 },
        // 收科人
        receiver: { type: String },
        // 收科地区
        receiverArea: { type: String },
        // 备注
        remark: { type: String },
        // 项目及使用地点
        projectAndLocation: { type: String },

        _id: false,
      },
    ],
  },
  {
    timestamps: true,
  }
);

const SuppliesInventoryLog = mongoose.model(
  "SuppliesInventoryLog",
  suppliesInventoryLogSchema
);

module.exports = { SuppliesInventoryLog, suppliesInventoryLogSchema };
