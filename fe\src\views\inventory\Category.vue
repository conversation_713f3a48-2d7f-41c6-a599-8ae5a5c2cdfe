<template>
  <div class="category-root">
    <div class="category-header">
      <div class="category-header-title">
        <span>类别设置</span>
      </div>
      <div class="category-header-action">
        <el-button
          type="primary"
          :icon="Plus"
          @click="categoryDialogRef?.open('add')"
        >
          新增类别
        </el-button>
      </div>
    </div>
    <div class="category-container">
      <div v-loading="categoryLoading" class="category-content">
        <TreeTable :columns="categoryColumns" :data="categoryList">
          <template #operations="{ row }">
            <el-button
              link
              type="primary"
              :disabled="!canAction(row)"
              @click="categoryDialogRef?.open('add', row)"
            >
              添加分类
            </el-button>
            <el-button
              link
              type="primary"
              :disabled="!canAction(row)"
              @click="categoryDialogRef?.open('edit', row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="primary"
              :disabled="!canAction(row)"
              :loading="moveLoading.includes(row.id)"
              @click="moveCategory(row, 'up')"
            >
              上移
            </el-button>
            <el-button
              link
              type="primary"
              :disabled="!canAction(row)"
              :loading="moveLoading.includes(row.id)"
              @click="moveCategory(row, 'down')"
            >
              下移
            </el-button>
            <el-button
              link
              type="primary"
              :disabled="!canAction(row)"
              :loading="deleteLoading.includes(row.id)"
              @click="deleteCategory(row)"
            >
              删除
            </el-button>
          </template>
        </TreeTable>
      </div>
    </div>
    <CategoryDialog ref="categoryDialogRef" @refresh="getCategoryList" />
  </div>
</template>

<script setup>
import { SearchContainer, TreeTable } from "@/base-components";
import { onMounted, reactive, ref } from "vue";
import { Plus, ArrowDown, ArrowRight } from "@element-plus/icons-vue";
import { categoryColumns } from "./config";
import CategoryDialog from "./components/CategoryDialog.vue";
import {
  getSuppliesClassTreeApi,
  deleteSuppliesClassApi,
  moveSuppliesClassApi,
} from "@/api";
import { SystemPrompt, toastError } from "@/utils";

const categoryLoading = ref(false);
const categoryList = ref([]);

const getCategoryList = async (params) => {
  categoryLoading.value = true;
  getSuppliesClassTreeApi()
    .then((res) => {
      categoryList.value = res.data.data;
      categoryLoading.value = false;
    })
    .catch((err) => {
      categoryLoading.value = false;
      toastError(err, "获取类别列表失败");
    });
};

onMounted(() => {
  getCategoryList();
});

/* ===================================== 操作 ===================================== */
const categoryDialogRef = ref();

const canAction = (row) => {
  return !(
    deleteLoading.value.includes(row.id) ||
    deleteLoading.value.includes(row.parentId) ||
    moveLoading.value.includes(row.id) ||
    moveLoading.value.includes(row.parentId)
  );
};

// 删除类别
const deleteLoading = ref([]);
function deleteCategory(row) {
  SystemPrompt(`将连同下级分类一起删除，确定要删除吗？`).then(() => {
    deleteLoading.value.push(row.id);
    deleteSuppliesClassApi(row.id)
      .then(() => {
        getCategoryList();
        Message.success("删除成功");
      })
      .catch((err) => {
        toastError(err, "删除失败");
      })
      .finally(() => {
        deleteLoading.value = deleteLoading.value.filter(
          (item) => item !== row.id
        );
      });
  });
}

// 上移/下移类别
const moveLoading = ref([]);
function moveCategory(row, direction) {
  moveLoading.value.push(row.id);
  moveSuppliesClassApi(row.id, direction)
    .then(() => {
      getCategoryList();
    })
    .catch((err) => {
      toastError(err, "移动失败");
    })
    .finally(() => {
      moveLoading.value = moveLoading.value.filter((item) => item !== row.id);
    });
}
</script>

<style lang="less" scoped>
.category-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
  }

  .category-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .category-content {
      margin-top: 10px;
      flex: 1;
      min-height: 0px;
    }
  }

  .category-name {
    display: flex;
    align-items: center;
    gap: 8px;

    .expand-icon {
      font-size: 12px;
      color: #909399;
      cursor: pointer;
    }
  }
}
</style>
