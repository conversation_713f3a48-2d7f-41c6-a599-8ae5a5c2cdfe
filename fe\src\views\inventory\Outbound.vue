<template>
  <div class="outbound-root">
    <div class="outbound-header">
      <div class="outbound-header-title">
        <TabsRouter :tabsConfig="LogRoutes" />
      </div>
      <div class="outbound-header-action">
        <el-button type="primary" :icon="Plus" @click="handleAddOutbound">
          新建出库单
        </el-button>
      </div>
    </div>
    <div class="outbound-container">
      <div class="outbound-search">
        <SearchContainer
          @queryBtnClick="handleSearch"
          @resetBtnClick="handleReset"
        >
          <el-form-item label="出库单编号">
            <el-input
              v-model.trim="searchForm.outboundNumber"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="出库时间">
            <el-date-picker
              v-model="searchForm.outboundDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="出库类型">
            <el-select
              v-model="searchForm.outboundType"
              placeholder="全部"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="领料出库" value="MATERIAL" />
              <el-option label="调拨出库" value="TRANSFER" />
              <el-option label="报废出库" value="SCRAP" />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select
              v-model="searchForm.auditStatus"
              placeholder="全部"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="待审核" value="PENDING" />
              <el-option label="已通过" value="APPROVED" />
              <el-option label="已拒绝" value="REJECTED" />
            </el-select>
          </el-form-item>
        </SearchContainer>
      </div>
      <div class="outbound-content">
        <CommonTable
          ref="tableRef"
          :columns="outboundColumns"
          :requestApi="getOutboundListApi"
          :requestParams="requestParams"
          :dataCallback="dataCallback"
        >
          <template #outboundType="{ row }">
            <el-tag :type="getOutboundTypeTag(row.outboundType)">
              {{ getOutboundTypeText(row.outboundType) }}
            </el-tag>
          </template>
          <template #auditStatus="{ row }">
            <el-tag :type="getAuditStatusTag(row.auditStatus)">
              {{ getAuditStatusText(row.auditStatus) }}
            </el-tag>
          </template>
          <template #operations="{ row }">
            <el-button link type="primary" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.auditStatus === 'PENDING'"
              link
              type="primary"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.auditStatus === 'PENDING'"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </CommonTable>
      </div>
    </div>

    <!-- 新增/编辑出库单弹窗 -->
    <CommonDialog
      v-model:visible="outboundDialog.visible"
      :title="outboundDialog.type === 'edit' ? '编辑出库单' : '新建出库单'"
      :confirm-callback="submitOutbound"
      :btn-loading="submitLoading"
      width="800px"
    >
      <el-form
        ref="outboundFormRef"
        :model="outboundForm"
        :rules="outboundRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出库单编号" prop="outboundNumber">
              <el-input
                v-model.trim="outboundForm.outboundNumber"
                placeholder="系统自动生成"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库类型" prop="outboundType">
              <el-select
                v-model="outboundForm.outboundType"
                placeholder="请选择出库类型"
                style="width: 100%"
              >
                <el-option label="领料出库" value="MATERIAL" />
                <el-option label="调拨出库" value="TRANSFER" />
                <el-option label="报废出库" value="SCRAP" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出库仓库" prop="warehouse">
              <el-select
                v-model="outboundForm.warehouse"
                placeholder="请选择仓库"
                style="width: 100%"
              >
                <el-option label="成都仓库" value="CHENGDU" />
                <el-option label="重庆仓库" value="CHONGQING" />
                <el-option label="西安仓库" value="XIAN" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="制单人" prop="creator">
              <el-input
                v-model.trim="outboundForm.creator"
                placeholder="请输入制单人"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model.trim="outboundForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import {
  TabsRouter,
  SearchContainer,
  CommonTable,
  CommonDialog,
} from "@/base-components";
import { LogRoutes } from "./config";
import { Message, SystemPrompt } from "@/utils";
import { cloneDeep } from "lodash";

/* ====================================== 表格配置 ====================================== */
const outboundColumns = [
  {
    prop: "outboundNumber",
    label: "出库单编号",
    width: 180,
    checked: true,
  },
  {
    prop: "outboundDate",
    label: "出库日期",
    width: 120,
    checked: true,
  },
  {
    prop: "outboundWarehouse",
    label: "出库仓库",
    width: 120,
    checked: true,
  },
  {
    prop: "outboundType",
    label: "出库类型",
    width: 120,
    checked: true,
  },
  {
    prop: "suppliesQuantity",
    label: "收货数量 (人数)",
    width: 150,
    checked: true,
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120,
    checked: true,
  },
  {
    prop: "creator",
    label: "制单人",
    width: 100,
    checked: true,
  },
  {
    prop: "createTime",
    label: "制单时间",
    width: 160,
    checked: true,
  },
  {
    prop: "operations",
    label: "操作",
    width: 180,
    fixed: "right",
    checked: true,
  },
];

/* ====================================== 搜索和数据 ====================================== */
const searchForm = reactive({
  outboundNumber: "",
  outboundDate: [],
  outboundType: "",
  auditStatus: "",
});

const requestParams = reactive({
  filters: "",
});

const tableRef = ref();

// 模拟API函数
const getOutboundListApi = (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟数据
      const mockData = [
        {
          id: 1,
          outboundNumber: "CK20250228001",
          outboundDate: "2025-02-28",
          outboundWarehouse: "成都仓库",
          outboundType: "MATERIAL",
          suppliesQuantity: "阿文",
          auditStatus: "PENDING",
          creator: "阿文",
          createTime: "2025-02-28 15:43:26",
        },
        {
          id: 2,
          outboundNumber: "CK20250227001",
          outboundDate: "2025-02-27",
          outboundWarehouse: "重庆仓库",
          outboundType: "TRANSFER",
          suppliesQuantity: "阿文",
          auditStatus: "REJECTED",
          creator: "阿文",
          createTime: "2025-02-27 15:43:26",
        },
        {
          id: 3,
          outboundNumber: "CK20250226001",
          outboundDate: "2025-02-26",
          outboundWarehouse: "西安仓库",
          outboundType: "MATERIAL",
          suppliesQuantity: "阿文",
          auditStatus: "APPROVED",
          creator: "阿文",
          createTime: "2025-02-26 15:43:26",
        },
        {
          id: 4,
          outboundNumber: "CK20250225001",
          outboundDate: "2025-02-25",
          outboundWarehouse: "西安仓库",
          outboundType: "MATERIAL",
          suppliesQuantity: "阿文",
          auditStatus: "APPROVED",
          creator: "阿文",
          createTime: "2025-02-25 15:43:26",
        },
      ];

      let filteredData = [...mockData];

      // 根据搜索条件过滤
      if (searchForm.outboundNumber) {
        filteredData = filteredData.filter((item) =>
          item.outboundNumber.includes(searchForm.outboundNumber)
        );
      }

      if (searchForm.outboundType) {
        filteredData = filteredData.filter(
          (item) => item.outboundType === searchForm.outboundType
        );
      }

      if (searchForm.auditStatus) {
        filteredData = filteredData.filter(
          (item) => item.auditStatus === searchForm.auditStatus
        );
      }

      const total = filteredData.length;
      const { offset = 0, limit = 10 } = params;
      const start = offset;
      const end = start + limit;
      const rows = filteredData.slice(start, end);

      resolve({
        data: {
          data: {
            rows,
            pageElements: {
              totalElements: total,
            },
          },
        },
      });
    }, 500);
  });
};

// CommonTable数据回调函数
function dataCallback(res) {
  const data = res.data?.data?.rows || [];
  return {
    tableRows: data,
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

/* ====================================== 搜索操作 ====================================== */
// 搜索
const handleSearch = () => {
  const filters = [];
  for (let key in searchForm) {
    if (searchForm[key]) {
      if (
        key === "outboundDate" &&
        Array.isArray(searchForm[key]) &&
        searchForm[key].length === 2
      ) {
        filters.push(`startDate=${searchForm[key][0]}`);
        filters.push(`endDate=${searchForm[key][1]}`);
      } else {
        filters.push(`${key}=${searchForm[key]}`);
      }
    }
  }
  requestParams.filters = filters.join(",");
};

// 重置
const handleReset = () => {
  searchForm.outboundNumber = "";
  searchForm.outboundDate = [];
  searchForm.outboundType = "";
  searchForm.auditStatus = "";
  requestParams.filters = "";
};

/* ====================================== 状态处理函数 ====================================== */
// 出库类型标签
const getOutboundTypeTag = (type) => {
  const tagMap = {
    MATERIAL: "primary",
    TRANSFER: "success",
    SCRAP: "warning",
  };
  return tagMap[type] || "";
};

const getOutboundTypeText = (type) => {
  const textMap = {
    MATERIAL: "领料出库",
    TRANSFER: "调拨出库",
    SCRAP: "报废出库",
  };
  return textMap[type] || type;
};

// 审核状态标签
const getAuditStatusTag = (status) => {
  const tagMap = {
    PENDING: "warning",
    APPROVED: "success",
    REJECTED: "danger",
  };
  return tagMap[status] || "";
};

const getAuditStatusText = (status) => {
  const textMap = {
    PENDING: "待审核",
    APPROVED: "已通过",
    REJECTED: "已拒绝",
  };
  return textMap[status] || status;
};

/* ====================================== 出库单操作 ====================================== */
// 出库单表单
const outboundDialog = reactive({
  visible: false,
  type: "add",
});

const initOutboundForm = {
  id: "",
  outboundNumber: "",
  outboundType: "",
  warehouse: "",
  creator: "",
  remark: "",
};

const outboundForm = ref(cloneDeep(initOutboundForm));

const outboundRules = {
  outboundType: [
    { required: true, message: "请选择出库类型", trigger: "change" },
  ],
  warehouse: [{ required: true, message: "请选择出库仓库", trigger: "change" }],
  creator: [{ required: true, message: "请输入制单人", trigger: "blur" }],
};

const outboundFormRef = ref();
const submitLoading = ref(false);

// 新增出库单
const handleAddOutbound = () => {
  outboundForm.value = cloneDeep(initOutboundForm);
  outboundForm.value.outboundNumber = generateOutboundNumber();
  outboundDialog.type = "add";
  outboundDialog.visible = true;
  outboundFormRef.value?.clearValidate();
};

// 编辑出库单
const handleEdit = (row) => {
  outboundForm.value = cloneDeep(row);
  outboundDialog.type = "edit";
  outboundDialog.visible = true;
  outboundFormRef.value?.clearValidate();
};

// 查看出库单
const handleView = (row) => {
  Message.info("查看功能待开发");
};

// 删除出库单
const handleDelete = (row) => {
  SystemPrompt("是否删除该出库单？", "warning").then(() => {
    Message.success("删除成功");
    tableRef.value.refreshTableData();
  });
};

// 提交出库单
const submitOutbound = () => {
  outboundFormRef.value.validate((valid) => {
    if (!valid) return;

    submitLoading.value = true;

    // 模拟API调用
    setTimeout(() => {
      Message.success(outboundDialog.type === "edit" ? "编辑成功" : "新增成功");
      outboundDialog.visible = false;
      submitLoading.value = false;
      tableRef.value.refreshTableData();
    }, 1000);
  });
};

// 生成出库单编号
const generateOutboundNumber = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const random = String(Math.floor(Math.random() * 1000)).padStart(3, "0");
  return `CK${year}${month}${day}${random}`;
};
</script>

<style lang="less" scoped>
.outbound-root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .outbound-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 0px 20px 0 0;
    flex-shrink: 0;

    .outbound-header-title {
      :deep(.custom-tabs-header) {
        height: 50px;
        & a {
          padding: 16px;
        }
      }
    }

    .outbound-header-action {
      display: flex;
      gap: 10px;
    }
  }

  .outbound-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .outbound-search {
      flex-shrink: 0;
      background: #f5f5f5;
      border-radius: 10px;
      padding: 0px 20px;
      box-sizing: border-box;
      margin-bottom: 10px;
    }

    .outbound-content {
      flex: 1;
      min-height: 0px;
    }
  }
}

/* 弹窗样式优化 */
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #333;
        font-weight: 600;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      tr {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 按钮样式优化 */
:deep(.el-button) {
  &.is-link {
    padding: 0;
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}

/* 搜索容器样式优化 */
:deep(.search-container) {
  .el-form {
    .el-form-item {
      margin-bottom: 16px;

      .el-form-item__label {
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
