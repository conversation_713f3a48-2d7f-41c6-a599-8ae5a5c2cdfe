import axios from "./axios-instance";

// 获取物资类别树
export function getSuppliesClassTreeApi() {
  return axios.get("/api/supplies/get-supplies-class");
}

// 新建物资类别
export function createSuppliesClassApi(data) {
  return axios.post("/api/supplies/add-supplies-class", data, {
    headers: {
      checkSensitive: true,
    },
  });
}

// 编辑物资类别
export function editSuppliesClassApi(data) {
  return axios.put("/api/supplies/edit-supplies-class", data, {
    headers: {
      checkSensitive: true,
    },
  });
}

// 删除物资类别
export function deleteSuppliesClassApi(id) {
  return axios.delete(`/api/supplies/delete-supplies-class/${id}`);
}

// 上移/下移物资类别
export function moveSuppliesClassApi(id, direction) {
  return axios({
    url: `/api/supplies/move-supplies-class`,
    method: "put",
    data: { id, direction },
  });
}
