const { SuppliesClass } = require("../models/SuppliesClass");
const { v4: uuid } = require("uuid");

// 获取备件类别
module.exports.getSuppliesClass = async (req, res) => {
  try {
    const data = await SuppliesClass.find().lean();
    // 递归组装树结构
    function buildTree(list, parentId = "") {
      return list
        .filter((item) => item.parentId === parentId)
        .sort((a, b) => a.sort - b.sort)
        .map((item) => ({
          ...item,
          children: buildTree(list, item.id),
        }));
    }
    const tree = buildTree(data);
    res.status(200).json({
      success: true,
      data: tree,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取备件类别失败",
      error: error.message,
    });
  }
};

// 添加备件类别
module.exports.addSuppliesClass = async (req, res) => {
  try {
    const { name, remark, code, parentId } = req.body;
    const id = uuid();
    //当前类别的其他兄弟数据
    const brothers = await SuppliesClass.find({ parentId });
    await SuppliesClass.create({
      name,
      remark,
      code,
      id,
      parentId,
      sort: brothers.length + 1,
    });
    res.status(200).json({
      success: true,
      message: "添加备件类别成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "添加备件类别失败",
      error: error.message,
    });
  }
};

// 编辑备件类别
module.exports.editSuppliesClass = async (req, res) => {
  try {
    const { name, remark, code, id } = req.body;
    await SuppliesClass.findOneAndUpdate({ id }, { name, remark, code });
    res.status(200).json({
      success: true,
      message: "编辑备件类别成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "编辑备件类别失败",
      error: error.message,
    });
  }
};

// 删除备件类别
module.exports.deleteSuppliesClass = async (req, res) => {
  try {
    const { id } = req.params;
    await SuppliesClass.deleteMany({
      $or: [{ id }, { parentId: id }],
    });
    res.status(200).json({
      success: true,
      message: "删除备件类别成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "删除备件类别失败",
      error: error.message,
    });
  }
};

// 上移/下移备件类别
module.exports.moveSuppliesClass = async (req, res) => {
  try {
    const { id, direction } = req.body;
    const target = await SuppliesClass.findOne({ id });
    if (direction === "up") {
      const prev = await SuppliesClass.findOne({
        parentId: target.parentId,
        sort: { $lt: target.sort },
      }).sort({ sort: -1 });
      if (prev) {
        await SuppliesClass.updateOne({ id }, { sort: prev.sort });
        await SuppliesClass.updateOne({ id: prev.id }, { sort: target.sort });
      } else {
        {
          return res.status(500).json({
            success: true,
            message: "已到顶部",
          });
        }
      }
    } else {
      if (target.sort === target.length) {
        return res.status(500).json({
          success: true,
          message: "已到底部",
        });
      }
      const next = await SuppliesClass.findOne({
        parentId: target.parentId,
        sort: { $gt: target.sort },
      }).sort({ sort: 1 });
      if (next) {
        await SuppliesClass.updateOne({ id }, { sort: next.sort });
        await SuppliesClass.updateOne({ id: next.id }, { sort: target.sort });
      } else {
        return res.status(500).json({
          success: true,
          message: "已到底部",
        });
      }
    }

    res.status(200).json({
      success: true,
      message: "移动备件类别成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "移动备件类别失败",
      error: error.message,
    });
  }
};
